@formItemLabelPaddingTop: 5px;

// Form
.pima-form {
  .ivu-form-item {
    .ivu-form-item-label {
      color: #3b3c3d;
      font-size: @fontSize15;
      line-height: @lineHeight22;
      padding: @formItemLabelPaddingTop 19px @formItemLabelPaddingTop 0;
      position: relative;
    }

    .ivu-form-item-content {
      display: flex;
      font-size: @fontSize14;
      color: @colorGrey300;
      word-break: break-word;
      line-height: @lineHeight40;
    }

    &:not(.no-colon) {
      .ivu-form-item-label {
        &::after {
          content: ':';
          padding-left: 5px;
          padding-right: 10px;
          position: absolute;
          top: @formItemLabelPaddingTop;
          right: 0;
        }
      }
    }

    &.no-colon {
      .ivu-form-item-label {
        padding-right: 0;
      }
    }

    &.read-only {
      margin-bottom: 0;
    }
  }

  .ivu-form-item-error {
    margin-bottom: 24px;

    & .ivu-input,
    & .ivu-input-number,
    & .ivu-input-textarea,
    & .ivu-select-selection,
    .pima-select-input-people-search {
      border: 1px solid @colorRed;
    }
  }

  &.ivu-form-label-left {
    .ivu-form-item-content {
      justify-content: flex-end;
    }
  }

  &.read-only {
    .ivu-form-item {
      margin-bottom: 0;
    }
  }
}

.pima-form-item-error {
  .ivu-input {
    box-shadow: none;

    &:active {
      box-shadow: none;
    }

    &:focus {
      box-shadow: none;
    }
  }
}
