.pima-table-scroll {
  @media screen and (max-width: @screenXlMin) {
    max-width: calc(100vw - @siderWidth - @mainMargin - @mainMargin - 4px);
  }

  > .table-wrap {
    // overflow: auto;

    :deep(& > *) {

      @w-sider: 172px;
      @w-padding: 40px;
      @w-scroll: 18px;
      // min-width: calc(1920px - @w-sider - @w-padding - @w-scroll);
      // 可以这样写, 但是每个 table column 要设置 minWidth
      min-width: calc(100vw - @w-sider - @w-padding - @w-scroll);
    }
  }

  > .paginator-wrap {
    display: flex;
    justify-content: flex-end;
    // 防止被下载悬浮框遮挡
    margin-bottom: 90px;
    padding: 16px 24px;
  }
}
