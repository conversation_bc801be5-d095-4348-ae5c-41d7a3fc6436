// Radio
.pima-radio-default {
  .ivu-radio-checked {
    .ivu-radio-inner {
      border-color: @colorPrimary100;

      &:after {
        background-color: @colorPrimary100;
      }
    }
  }

  .ivu-radio-wrapper {
    padding-left: 25px;
    height: auto;
    min-height: 32px;
    text-indent: -12px;
    white-space: normal;
  }

  .ivu-radio-wrapper:not(:last-child) {
    margin-right: 35px;
  }
}

.pima-radio-group-vertical {
  .ivu-radio {
    margin-right: 4px;
    transform: translateY(-1px);

    .ivu-radio-inner {
      border-radius: @radioBorderRadius;
    }
  }

  .ivu-radio-checked {
    .ivu-radio-inner {
      background-color: @colorWhite;
      border-color: @colorPrimary100;

      &:after {
        top: 4px;
        left: 4px;
        width: 6px;
        height: 6px;
        border-radius: @radioBorderRadius;
        background-color: @colorPrimary100;
      }
    }
  }

  .ivu-radio-focus {
    box-shadow: none;
  }

  .ivu-radio-wrapper {
    margin-right: 24px;
    padding-left: 25px;
    height: auto;
    min-height: 32px;
    text-indent: -12px;
    white-space: normal;
  }

  .ivu-radio-disabled {
    .ivu-radio-inner {
      background-color: #fff;
      border-color: #e9eaeb;
    }

    &.ivu-radio-checked { 
      .ivu-radio-inner {
        background-color: #fff;
        border-color: #e9eaeb;

        &::after {
          background-color: #bdbdbd;
        }
      }
    }
  }
}
