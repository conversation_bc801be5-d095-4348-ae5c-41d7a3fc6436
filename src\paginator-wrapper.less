@import "./select.less";


// 分页
.pima-paginator-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-align: right;

  .paginator {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    font-size: @fontSize14;

    .total {
      position: absolute;
      top: 50%;
      left: 0;
      margin-right: 20px;
      color: #777;
      font-weight: 400;
      transform: translateY(-50%);
      line-height: 1.5;

      .count {
        color: @colorPrimary100;
      }
    }

    .paget-index-input {
      width: 60px;
      height: 32px;
      border: 1px solid @borderColor;
      border-radius: @fillet3;
      color: @colorGrey200;
      background-color: @colorWhite;

      &.ivu-input-number-default {
        box-shadow: none;
      }
    
      &:focus,
      &:focus-within,
      &:hover {
        border-color: @borderHoverColor;
        box-shadow: none;
        outline: 0;
      }

      .ivu-input-number-input {
        height: 32px;
        font-size: @fontSize14;
      }

      .ivu-input-number-input-wrap {
        height: 32px;
      }

      .ivu-input-number-handler-wrap {
        .ivu-input-number-handler {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 16px;

          .ivu-input-number-handler-down-inner,
          .ivu-input-number-handler-up-inner {
            position: relative;
            right: 0;
          }
        }
      }
    }

    .jump-button {
      height: 32px;
      font-size: @fontSize14;
      background-color: @colorPrimary100;
      border-color: @borderHoverColor;

      &:hover {
        background-color: @colorPrimary200;
      }
    }

    .jump-to {
      margin-right: 10px;
    }

    .page {
      margin: 0 10px;
    }
  }

  .ivu-page {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;

    .ivu-select {
      &:extend(.pima-select all);

      .ivu-select-item {
        &::after {
          content: none;
        }
      }
    }
  
    .ivu-select-dropdown {
      position: fixed;
    }
  }

  .ivu-page .ivu-page-options .ivu-select-dropdown {
    position: absolute;
  }

  .page-total {
    margin-right: 20px;
    color: @colorGrey300;
    font-size: @fontSize14;
  }

  .ivu-page-item,
  .ivu-page-item-jump-next,
  .ivu-page-item-jump-prev {
    min-width: 32px;
    height: 32px;
    margin-right: 0;
    color: @colorGrey200;
    font-size: @fontSize14;
    line-height: 32px;
    border: 1px solid @borderColor;
    border-radius: @fillet8;
  }

  .ivu-page-item {
    &:hover {
      border-color: @colorPrimary100;

      a {
        color: @colorPrimary100;
      }
    }
  }

  .ivu-page-item-active {
    color: @colorWhite;
    background: @colorPrimary100;
    border-color: @borderHoverColor;

    &:hover {
      background-color: @colorPrimary200;
      border-color: @borderHoverColor;

      a {
        color: @colorWhite;
      }
    }
  }

  .ivu-page-item-active a,
  .ivu-page-item-active:hover a {
    color: @colorWhite;
  }

  .ivu-page-custom-text {
    width: unset;
    min-width: 70px;
    height: 32px;
    padding: 0 20px;
    line-height: 32px;
    border: 1px solid white;
    border-radius: 0;

    &:not(.ivu-page-disabled) {
      &:hover {
        &:hover {
          border: 1px solid @colorPrimary100;
        }
      }
    }

    &.ivu-page-custom-text {
      &:not(.ivu-page-disabled) {
        &:hover {
          border: none;
        }
      }
    }
  }

  .ivu-page-prev {
    border: none;
    background-color: unset;

    &:not(.ivu-page-disabled) {
      &:hover {
        a {
          color: unset;
        }
      }
    }

    .ivu-icon {
      font-size: 16px;
    }

    &.ivu-page-custom-text {
      &:not(.ivu-page-disabled) {
        &:hover {
          border: none;
        }
      }
    }
  }

  .ivu-page-next {
    border: none;
    background-color: unset;

    &:not(.ivu-page-disabled) {
      &:hover {
        a {
          color: unset;
        }
      }
    }

    .ivu-icon {
      font-size: 16px;
    }
  }

  .ivu-page-options {
    order: -1;
  }

  .ivu-select-single {
    .ivu-select-item {
      font-size: @fontSize14;
    }

    .ivu-select-item-selected,
    .ivu-select-item-selected:hover {
      color: @colorPrimary100;
    }
  }

  .ivu-page .ivu-select.ivu-select-single .ivu-select-selection {
    width: 112px;
    height: 32px;
    min-height: 32px;
    outline: none;
    border: 1px solid #ededed;
    border-radius: @fillet8;

    &:hover,
    &:focus-within {
      border-color: @borderHoverColor;
      outline: none;
    }

    div:first-of-type {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;

      span {
        font-size: @fontSize14;
      }
    }

    .ivu-icon-ios-arrow-down {
      width: 16px;
      height: 16px;
      background: data-uri(@simpleSearchSelectArrowPath) no-repeat center center;
      background-size: 16px 16px;

      &::before {
        display: none;
      }
    }

    .ivu-select-selected-value {
      height: 32px;
      padding-left: 12px;
      line-height: 32px;
    }
  }
}
