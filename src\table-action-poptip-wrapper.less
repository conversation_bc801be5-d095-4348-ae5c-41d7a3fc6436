@import (reference) "./root.less";

.pima-table-action-poptip-wrapper {
  body & {
    min-width: 80px;
  }

  .ivu-poptip-inner {
    padding: 10px;
    border: 0.5px solid #dcdcdc;
    border-radius: 6px;
    box-shadow: 0 6px 30px 5px fade(#000, 5%), 0 16px 24px 2px fade(#000, 4%), 0 8px 10px -5px fade(#000, 8%);
  }

  .ivu-poptip-body {
    padding: 0;
    text-align: center;

    .ivu-poptip-body-content {
      overflow: unset;
    }

    .ivu-btn-group-vertical {
      width: 100%;

      .ivu-btn-text {
        width: 40px;
        height: @lineHeight40;
        line-height: @lineHeight40;
    
        &:hover:not([disabled]) {
          color: @colorPrimary100;
          background-color: @tableMoreActionRadioButtonHoverBackgroundColor;
        }

        .icon {
          width: 40px;
        }
      }
    }
  }

  &.text-btn {
    body & {
      min-width: 120px;
    }

    .ivu-poptip-body .ivu-btn-group-vertical .ivu-btn-text {
      width: -webkit-fill-available;
    }
  }
}
