.pima-import {
  .pima-import-modal-main {
    position: fixed;
    top: 100px;
    left: 50%;
    z-index: 11;
    width: 50%;
    min-width: 500px;
    max-width: 800px;
    min-height: 300px;
    max-height: 400px;
    padding: 70px 30px 15px;
    overflow: hidden;
    font-size: @fontSize16;
    background: @colorWhite;
    border-radius: 3px;
    box-shadow: 0 5px 10px -3px lighten(#000, 94%);
    transform: translateX(-50%);

    .import-header {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 10px 10px 10px 16px;

      .title {
        color: lighten(#000, 15%);
        font-weight: @fontWeightBold;
        font-size: @fontSize14;
      }

      .close {
        width: 20px;
        height: 22px;
        background: data-uri("../assets/img/import/close.png") no-repeat center center / 50% 50%;
        cursor: pointer;
      }
    }

    .import-content {
      min-height: 150px;
      padding: 15px 30px;
      border: 1px dashed @borderColor;
      border-radius: 10px;

      .initial-panel {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 10px;

        button {
          width: 90px;
          height: 24px;
          margin-top: 12px;
          margin-bottom: 5px;
          padding: 2px 10px;
          color: @colorWhite;
          line-height: normal;
          font-size: @fontSize12;
          background: @colorPrimary100;
          border: none;
          border-radius: @fillet3;
          outline: none;
          cursor: pointer;

          &:focus {
            box-shadow: 0 0 5px 5px lighten(#dcdcdc, 70%);
          }
        }

        a {
          color: @colorPrimary100;
          font-size: @fontSize12;
          text-decoration: underline;
        }
      }
    }

    .upload-notices {
      margin-top: 10px;
      color: @colorGrey400;
      font-size: @fontSize10;
    }
  }

  .pima-import-modal-progress {
    position: fixed;
    top: 100px;
    left: 50%;
    z-index: 12;
    width: 500px;
    min-height: 120px;
    padding: 15px 25px;
    overflow: hidden;
    background: @colorWhite;
    border-radius: 5px;
    box-shadow: 0 3px 8px -2px lighten(#000, 94%);
    transform: translateX(-50%);

    .progress-panel {
      color: @colorGrey300;
      font-size: @fontSize12;

      .progress-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 0 10px;

        .filename {
          color: @colorGrey200;
          font-size: @fontSize14;
        }
      }

      .progress-content {
        display: flex;
        align-items: center;
        margin: 0 auto;
        padding-bottom: 10px;

        .progress-slot {
          position: relative;
          flex: 1;
          height: 8px;
          overflow: hidden;
          background: lightgray;
          border-radius: 3px;

          .progress-bar {
            position: absolute;
            left: -100%;
            width: 100%;
            height: 100%;
            background: @colorPrimary100;
            border-radius: @fillet3;
            transition: left 200ms ease;

            &.success {
              background: @colorGreen;
            }

            &.fail {
              background: @colorRed;
            }
          }
        }
      }

      .progress-footer {
        margin-top: 10px;
        text-align: right;

        .btn {
          width: auto;
          height: 24px;
          margin-left: 10px;
          padding: 2px 10px;
          font-size: @fontSize12;
          white-space: nowrap;
          border: none;
          border-radius: @fillet3;
          outline: none;
          cursor: pointer;
          appearance: none;

          &.btn-close {
            color: @colorPrimary100;
            background: transparent;
          }

          &.btn-download-error-result {
            color: @colorWhite;
            background: @colorPrimary100;
          }
        }
      }
    }
  }
}
