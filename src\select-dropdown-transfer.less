.pima-select-dropdown-transfer {
  &.ivu-select-dropdown.ivu-select-dropdown-transfer {
    .ivu-select-dropdown-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 4px;

      .ivu-select-item {
        position: relative;
        display: flex;
        align-items: center;
        padding: 2px 8px;
        height: 26px;
        border-radius: 3px;
        color: #444;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        text-overflow: ellipsis;
        overflow: hidden;

        &:hover {
          color: @colorGrey300;
          background-color: @selectItemHoverBackgroundColor;
        }
      }

      .ivu-select-item-selected,
      .ivu-select-item-focus {
        color: @colorPrimary100 !important; // UI库有important
        background: none;
      }
    }
  }
}