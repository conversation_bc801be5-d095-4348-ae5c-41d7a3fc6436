@import "./theme.less";

.pima-drawer-wrapper {
  &:extend(.pima-theme all);

  .pima-drawer-title {
    width: 100%;
    height: 50px;
    color: #fff;
    font-size: @fontSize16;
    line-height: 50px;
    text-align: center;
    background: @colorPrimary100;
  }

  .pima-drawer-body {
    flex: 1;
    overflow: auto;

    .pima-drawer-form-wrapper {
      position: relative;
      height: 100%;
      overflow: hidden;
    
      .pima-drawer-content-main {
        height: 100%;
        padding: 32px 48px 130px;
        overflow-y: auto;
      }

      .pima-drawer-content-footer {
        position: absolute;
        right: 30px;
        bottom: 0;
        left: 30px;
        z-index: 1;
        background: @colorWhite;

        .pima-btn {
          height: 40px;
          min-width: 120px;
        }
      }

      .pima-drawer-cover-tips {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        padding-top: 140px;
        background: @colorWhite;
      }
    }
  }

  .pima-drawer-form-block {
    position: relative;

    &::after {
      position: absolute;
      right: -48px;
      bottom: 0;
      left: -48px;
      height: 1px;
      background: @drawerFormBlockBorderColor;
      content: "";
    }

    &:last-child::after {
      display: none;
    }
  }

  .pima-drawer-form-block + .pima-drawer-form-block {
    margin-top: 30px;
  }

  .pima-drawer-form-block-title {
    color: @colorGrey200;
    font-size: @fontSize16;
    font-weight: 500;
  }

  .pima-drawer-form-block-content {
    padding-top: 24px;
    padding-bottom: 10px;
  }

  .pima-drawer-footer {
    width: 100%;
    height: 52px;
    padding: 0 30px 20px;
  }

  .pima-button-close {
    position: absolute;
    top: 50%;
    left: 0;
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    color: @colorPrimary100;
    background: @colorWhite;
    transform: translate(-50%, -50%);

    .ivu-icon {
      cursor: pointer;

      &:hover {
        color: @colorPrimary100;
      }
    }
  }

  .ivu-drawer-body {
    display: flex;
    flex-direction: column;
    padding: 0;
    overflow: visible;
  }

  &.pima-student-drawer-wrapper {
    --primary-color: @colorGreen;
    --primary-hover-color: @colorGreen200;
    --primary-disabled-color: @colorGreen300;
  }
}
