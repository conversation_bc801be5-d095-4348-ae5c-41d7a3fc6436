.pima-select-people-tree {
  .root-node {
    background-color: #fff;
  }

  .head-node {
    max-height: 32px;
    overflow: hidden;
    transition: all 0.2s ease-in-out;

    & > li:first-of-type {
      position: relative;
      z-index: 10;
    }
  }

  .tree-node {
    line-height: 32px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    border-left: 2px solid transparent;

    &.selected {
      color: @colorPrimary100;
    }
  }

  .children-node {
    background-color: #f9f9f9;
  }

  .expand {
    max-height: 2000px;
  }

  .children-node-wrapper {
    background-color: #f9f9f9;
    overflow: hidden;
    transition: all 0.2s ease-in-out;
  }

  // .children-expand {
  //   transform: translateY(-200%);
  // }

  ul {
    li {
      list-style: none;
      padding: 0;
      margin: 0;
    }
  }

  .mouse-enter {
    background-color: @selectItemHoverBackgroundColor;
  }

  .mouse-leave {
    color: @colorGrey300;
    background-color: unset;
  }

  .arrow-down {
    margin-right: 5px;
    transition: all 0.2s ease-in-out;
  }
  
  .icon-expand {
    transform: rotate(-180deg);
  }
  
  .prefix-perch {
    padding: 0 9px;
  }
}

.pima-select-people-tree-filter {
  .no-data {
    height: 32px;
    line-height: 32px;
    text-align: center;
  }
}
