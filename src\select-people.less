.pima-select-people {
  .layout-tab {
    position: relative;
    display: flex;
    width: 80%;
    min-width: 600px;
    max-width: 1000px;
    min-height: 600px;
    max-height: 800px;
    margin: 100px auto;
    background-color: #fff;
    border: 1px solid #efefef;

    .container-head {
      padding: 0 20px;
      line-height: 32px;
      border-bottom: 1px solid #efefef;
    }
  
    .container-l {
      width: 160px;
      border-right: 1px solid #efefef;
    }

    .container-r {
      flex: 1;
    }
  
    .close-btn {
      position: absolute;
      top: -26px;
      right: -26px;
      width: 26px;
      height: 26px;
      background-color: fade(#fff, 80%);
      background-image: data-uri("../assets/img/import/close.png");
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 10px 11px;
      border-radius: 50%;
      cursor: pointer;
  
      &:hover {
        background-color: #fff;
      }
    }
  }

  .title-bar {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    height: 33px;
    padding: 0 20px;
    font-size: 12px;
    border-bottom: 1px solid #efefef;
  
    .right {
      display: flex;
      align-items: center;
      margin-left: auto;
      padding-right: 10px;
    }
  }

  .menu {
    &.ivu-menu-light {
      z-index: 10;
      background-color: #fcfcfc;
      border-bottom: 1px solid #efefef;

      .ivu-menu-item:not(.ivu-menu-submenu) {
        padding: 0;
        padding-left: 36px;
        color: fade(#000, 65%);
        font-size: 12px;
        line-height: 34px;
      }

      .ivu-menu-item-active:not(.ivu-menu-submenu) {
        background-color: #f5f5f5;
      }
  
      .ivu-menu-item-active:not(.ivu-menu-submenu)::after {
        right: unset;
        left: 0;
        background-color: #8d0306;
      }
  
      &::after {
        display: none;
      }
    }
  }

  .wrapper-select-department {
    padding: 20px;
  
    .search-bar {
      padding: 8px 24px;
    }
  
    .wrapper-tree {
      max-height: 400px;
      overflow-y: auto;
    }
  }

  .scroll-load-wrap {
    overflow: auto;
  
    .footer {
      padding: 0 10px;
    }
  }

  .select-all {
    padding: 20px;
  }

  .wrapper-select-people-associative {
    height: 100%;

    .default-transtion(@property : all) {
      transition: @property 0.4s ease-in-out;
    }

    .import-people::after {
      margin: 0 6px;
      color: #d8d8d8;
      vertical-align: 2px;
      content: "|";
    }

    .content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: calc(100% - 40px);
      padding: 20px;
    }

    .wrapper-import {
      .import-title {
        color: fade(#000, 85%);
        line-height: 28px;
        border-bottom: 2px solid #e2e2e2;
      }
      
      .import-content {
        height: 210px;
        overflow-y: auto;
      }

      .file-list {
        padding: 0 10px;
        font-size: 12px;
        line-height: 28px;
        border-bottom: 1px solid #e2e2e2;
      
        .file-name {
          margin-right: 10px;
        }
      }
    }

    .sp-associative-option {
      padding: 0 10px;
      cursor: pointer;
      transition: background-color 0.4s ease-in-out;

      &:hover,
      &.is-checked {
        background-color: #f3f5fa;
      }

      .option-content {
        padding: 5px 0;
    
        & > * {
          margin: 0;
        }

        .title {
          color: fade(#000, 65%);
          font-size: 14px;
          line-height: 20px;
        }

        .sp-high-light {
          color: #8d0306;
        }
      }

      &.is-checked {
        .option-content {
          .title {
            color: #8d0306;
          }
        }
      }
    }

    .select-people-primary-border {
      border: 1px solid #d8d8d8;
      .default-transtion(border-color);
  
      &:hover, &.active {
        border-color: @colorPrimary100;
      }
    }

    .ivu-form-item-error {
      .select-people-primary-border.in-form {
        &, &:hover, &.active {
          border-color: #ed4014;
        }
      }
    }

    .select-people-colon {
      &::after {
        content: ':';
        padding: 0 4px 0 2px;
      }
    }

    .select-people-associative-content {
      background: #fff;
    }

    .select-people-associative {
      width: 100%;

      .header {
        padding: 0px 10px;
        line-height: 31px;
    
        .input-keyword {
          width: 100%;
          font-size: 14px;
          line-height: 20px;
          color: #222;
          padding: 0;
    
          &::placeholder {
            color: rgba(0,0,0,0.45);
          }
    
          &, &:focus, &:hover {
            border: none;
            outline: none;
          }
        }
      }
    }
  }

  .select-people-tags-squared {
    text-align: left;

    .tag {
      margin-right: 8px;

      &.option-all {
        .tag-text {
          margin-right: 0;
        }
      }
    }

    &.select-disabled {
      .tag {
        cursor: not-allowed;
  
        .icon-ios-close {
          display: none;
        }
      }
    }
  }

  .select-people-tag {
    position: relative;
    display: inline-block;
    margin: 3px 8px 3px 0;
    padding: 0 30px 0 8px;
    line-height: 22px;
    background: #fafafa;
    border: 1px solid #e8eaec;
  
    .tag-text {
      color: #515a6e;
      font-size: 12px;
      line-height: 22px;
  
      &.unnamed {
        color: #aaa;
      }
    }
  
    .tag-remove-icon {
      position: absolute;
      top: 50%;
      right: 8px;
      display: inline-block;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }

  .select-people-button.ivu-btn {
    height: max-content;
    padding: 6px 12px;
    color: #222;
    font-size: 14px;
    line-height: 20px;
    white-space: normal;
    background: #fff;
    border: 1px solid #555;
    border-radius: 2px;
  
    &.small {
      padding: 2px 8px;
      font-size: 12px;
      line-height: 17px;
    }
  
    &.disabled {
      cursor: not-allowed;
      opacity: 0.45;
    }
  
    &.green {
      border-color: #06b03f;
  
      &.plain {
        &,
        &:hover {
          color: #06b03f;
        }
      }

      &:not(.plain) {
        color: #fff;
        background: #06b03f;
      }
    }

    &.blue {
      border-color: #17439d;
  
      &.plain {
        &,
        &:hover {
          color: #17439d;
        }
      }

      &:not(.plain) {
        color: #fff;
        background: #17439d;
      }
    }

    &.red {
      border-color: #ff5256;
  
      &.plain {
        &,
        &:hover {
          color: #ff5256;
        }
      }

      &:not(.plain) {
        color: #fff;
        background: #ff5256;
      }
    }
  }

  .select-people-text-button.ivu-btn {
    margin: 0;
    padding: 0;

    &,
    &:hover,
    &:focus,
    &:active {
      background-color: transparent;
      border: none;
      box-shadow: none;
    }
  
    &.underline {
      & > span {
        text-decoration: underline;
      }
    }
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: fade(#000, 6%);
    border-radius: 3px;
    box-shadow: inset 0 0 5px fade(#000, 8%);
  }

  ::-webkit-scrollbar-thumb {
    background: fade(#000, 12%);
    border-radius: 3px;
    box-shadow: inset 0 0 10px fade(#000, 20%);
  }
}