.pima-card-tab {
  &.ivu-tabs.ivu-tabs-card {
    > .ivu-tabs-bar {
      margin-bottom: 0;
      border-bottom: 0;

      .ivu-tabs-nav-container {
        height: 65px;
      }

      .ivu-tabs-nav-wrap {
        height: 100%;
      }

      .ivu-tabs-nav-next,
      .ivu-tabs-nav-prev {
        line-height: 65px;
      }

      .ivu-tabs-tab {
        position: relative;
        height: 65px;
        margin-right: 0;
        padding: 20px 24px;
        color: #8d8e8f;
        font-weight: 400;
        font-size: 18px;
        background-color: unset;
        border: 0;
        border-radius: 16px 16px 0 0;

        &:hover:not(.ivu-tabs-tab-disabled) {
          color: #3b3c3d;
          font-weight: 600;
        }

        &.ivu-tabs-tab-active {
          color: #3b3c3d;
          font-weight: 600;
          background-color: #fff;

          &::after {
            position: absolute;
            bottom: 8px;
            left: 50%;
            width: 24px;
            height: 4px;
            background-color: var(--primary-color);
            border-radius: 4px;
            transform: translateX(-50%);
            content: "";
          }
        }

        &.ivu-tabs-tab-disabled {
          color: #bcbcbb;
          pointer-events: auto;
          cursor: not-allowed;
        }
      }
    }

    > .ivu-tabs-content {
      height: 100%;
      padding: 28px;
      overflow-y: auto;
      background-color: #fff;
      border-radius: 16px 16px 0 0;

      .ivu-tabs-tabpane {
        padding-bottom: 30px;
      }
    }

    &.no-radius {
      > .ivu-tabs-content {
        border-top-left-radius: 0;
      }
    }
  }
}
