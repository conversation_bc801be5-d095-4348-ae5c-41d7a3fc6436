// Table
.pima-table-wrapper {
  border: unset;

  .ivu-table {
    background-color: unset;

    &::before {
      background-color: @tableBorderBottom;
    }
  }

  .ivu-table-header {
    th {
      padding: 0;
      min-height: 46px;
      border-bottom: 1px solid @tableBorderBottom;
      color: #494A4B;
      font-weight: @fontWeightBold;
      font-size: @fontSize16;
      background: fade(#fff, 50%);

      .ivu-table-cell {
        word-wrap: break-word;
        word-break: break-word;
      }
    }
  }

  .ivu-table-fixed-header {
    thead tr th {
      padding: 0;
      min-height: 46px;
      border-bottom: 1px solid @tableBorderBottom;
      color: #494A4B;
      font-weight: @fontWeightBold;
      font-size: @fontSize16;
      background-color: #f8fbfe;

      .ivu-table-cell {
        word-wrap: break-word;
        word-break: break-word;
      }
    }

    .ivu-table-column-left {
      min-height: 46px;
      color: @colorGrey200;
      font-weight: @fontWeightNormal;
      font-size: @fontSize16;
      background: @colorWhite;
      border-bottom: 1px solid @tableBorderBottom;

      .ivu-table-cell {
        word-wrap: break-word;
        word-break: break-word;
      }
    }
  }

  .ivu-table-fixed-body {
    background-color: #f8fbfe;
  }

  .ivu-table-tbody {
    td {
      height: 50px;
      color: #646566;
      font-weight: 400;
      font-size: @fontSize15;
      background-color: unset;
      border-bottom: 1px solid @tableBorderBottom;

      .ivu-table-cell {
        word-wrap: break-word;
        word-break: break-word;
        overflow: visible;

        .ivu-table-cell-slot {
          a {
            color: @colorPrimary100;
            font-weight: 600;
            line-height: 1.33;

            // &:hover {
            //   color: @colorPrimary200;
            // }
          }

          .text-theme-1 {
            color: #3b3c3d;
            font-weight: 400;
          }

          .text-theme-2 {
            color: #8d8e8f;
            font-weight: 300;
          }
        }
      }
    }
  }

  .ivu-table-row-hover {
    td {
      background-color: fade(#fff, 80%);
    }
  }

  .ivu-table-cell {
    padding: 16px 8px;
  }

  .ivu-table-tip {
    color: @colorGrey300;
    font-size: @fontSize14;
  }

  .ivu-table:after {
    display: none;
  }

  .ivu-spin {
    color: @colorPrimary100;
    border-right: unset;

    .ivu-spin-main {
      .ivu-spin-dot {
        background-color: @colorPrimary100;
      }
    }
  }

  .ivu-checkbox-wrapper {
    &.ivu-checkbox-wrapper-checked {
      .ivu-checkbox {
        &.ivu-checkbox-checked {
          .ivu-checkbox-inner {
            background-color: @colorPrimary100;
            border-color: @colorPrimary100;
          }
        }
      }
    }

    .ivu-checkbox {
      .ivu-checkbox-inner {
        width: 16px;
        height: 16px;

        &::after {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%) rotate(45deg) scale(1);
        }
      }

      &.ivu-checkbox-indeterminate {
        .ivu-checkbox-inner {
          background-color: @colorPrimary100;
          border-color: @colorPrimary100;

          &::after {
            top: 6px;
            left: 2px;
            transform: rotate(0) scale(1);
          }
        }
      }
    }

    .ivu-checkbox-input {
      &:focus {
        box-shadow: unset;
      }
    }
  }

  // 缩小边距，用于显示更多内容
  &.small-padding {
    .ivu-table-cell {
      padding: 14px 4px;
    }

    .ivu-table-tbody {
      td > .ivu-table-cell {
        padding: 5px 4px;
      }
    }
  }

  // 用于详情页内的表格
  &.pima-table-mini {
    border-left: 1px solid #f2f2f2;
    border-right: 1px solid #f2f2f2;

    .ivu-table-header {
      th {
        background-color: #f5f8fb;
      }
    }

    .ivu-table-tbody {
      td {
        background-color: #fff;
      }
    }

    .ivu-table-fixed-header {
      thead tr th {
        background-color: #f5f8fb;
      }
    }
  }
}
