.pima-forbidden {
  position: relative;
  width: 100%;
  height: 100%;
  color: @colorGrey300;
  font-size: @fontSize14;

  .tip-wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    text-align: center;
    transform: translate(-50%, -50%);

    .img-error {
      position: relative;
      width: 290px;
      height: 154px;

      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: data-uri(@forbiddenImagePath) no-repeat center center / 100% auto;
        content: " ";
      }
    }

    .tip {
      margin-top: 24px;

      .action {
        margin-top: 14px;

        .btn-go-back {
          width: 90px;
          height: 36px;
          color: @colorWhite;
          background-color: @colorPrimary100;
          border: none;
          border-radius: @fillet3;
          cursor: pointer;
        }
      }
    }
  }
}
