.pima-select-input-people-search {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  max-height: 500px;
  min-height: 32px;
  padding: 0 24px 0 10px;
  font-size: 14px;
  background-color: #fff;
  border: 1px solid #dadada;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  overflow: hidden auto;

  .ivu-tag {
    margin-top: 0;
  }

  .select-value {
    display: inline;
    color: #222;
    line-height: 32px;
  }

  .select-input {
    flex: 1;
    min-width: 50px;
    padding: 0;
    color: #515a6e;
    line-height: 32px;
    border: none;
    outline: none;

    &::placeholder {
      color: #c5c8ce;
      font-size: 14px;
    }
  }

  .select-placeholder {
    color: #c5c8ce;
    line-height: 2;
  }

  .suffix {
    position: absolute;
    top: 50%;
    right: 8px;
    font-size: 14px;
    transform: translateY(-50%);
  }

  .prefix {
    position: absolute;
    top: 50%;
    left: 8px;
    margin-right: 12px;
    font-size: 14px;
    transform: translateY(-50%);
  }

  .icon-down {
    color: #808695;
    transition: all 0.2s ease-in-out;
  }

  .icon-close {
    color: @colorPrimary100;
  }

  &:hover,
  &.focus {
    border-color: @colorPrimary100;

    .icon-down {
      color: @colorPrimary100;
    }
  }

  &.focus {
    .icon-down {
      transform: rotate(180deg);
    }
  }

  &.have-prefix {
    padding-left: 24px;
  }
}
