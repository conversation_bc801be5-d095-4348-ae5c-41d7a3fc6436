.pima-date-picker {
  .ivu-input {
    display: inline-block;
    width: 100%;
    height: 42px;
    line-height: 1.5;
    padding: 4px 16px;
    font-size: @fontSize14;
    border: 1px solid @borderColor;
    border-radius: @fillet5;
    color: @colorGrey200;
    background-color: @colorWhite;
    background-image: none;
    position: relative;
    cursor: text;
    -webkit-transition: border 0.2s ease-in-out, background 0.2s ease-in-out;
    transition: border 0.2s ease-in-out, background 0.2s ease-in-out;

    &::placeholder {
      color: @colorGrey500;
    }

    &::-moz-placeholder {
      color: @colorGrey500;
      opacity: 1;
    }

    &:-ms-input-placeholder {
      color: @colorGrey500;
    }

    &:-ms-input-placeholder {
      color: @colorGrey500;
    }

    &::-webkit-input-placeholder {
      color: @colorGrey500;
    }

    &:active {
      box-shadow: none;
      outline: 0;
    }

    &:hover:not([disabled]),
    &:focus:not([disabled]) {
      border-color: @colorPrimary100;
      box-shadow: none;
      outline: 0;
    }

    &[disabled],
    &[disabled]:hover {
      background-color: @disabledBackgroundColor;
      opacity: 1;
      cursor: not-allowed;
      color: @colorGrey400;
      border-color: @borderColor;
    }
  }

  &.ivu-date-picker-focused input {
    box-shadow: none;
  }

  .ivu-picker-panel-body {
    .ivu-date-picker-header {
      .ivu-picker-panel-icon-btn:hover,
      .ivu-date-picker-header-label:hover {
        color: @colorPrimary100;
      }
    }

    .ivu-picker-panel-content {
      .ivu-date-picker-cells {
        & span em {
          border-radius: 0;
        }

        .ivu-date-picker-cells-focused em {
          box-shadow: 0 0 0 1px @colorPrimary100 inset;
        }
  
        .ivu-date-picker-cells-cell:hover em {
          color: @colorPrimary100;
          background: @colorWhite;
          box-shadow: 0 0 0 1px @colorPrimary100 inset;
        }

        .ivu-date-picker-cells-cell-selected em,
        .ivu-date-picker-cells-cell-selected:hover em,
        .ivu-date-picker-cells-cell-today em:after {
          color: @colorWhite;
          background-color: @colorPrimary100;
        }
      }

      .ivu-date-picker-cells-month .ivu-date-picker-cells-cell-focused,
      .ivu-date-picker-cells-year .ivu-date-picker-cells-cell-focused {
        color: @colorWhite;
        background-color: @colorPrimary100;
      }

      .ivu-time-picker-cells {
        .ivu-time-picker-cells-list {
          .ivu-time-picker-cells-ul {
            .ivu-time-picker-cells-cell-selected {
              color: @colorPrimary100;
              background: @colorPrimary500;
            }

            .ivu-time-picker-cells-cell:hover {
              background: @colorPrimary500;
            }
          }
        }
      }
    }

    .ivu-picker-confirm {
      .ivu-btn-primary {
        background-color: @colorPrimary100;
        border-color: @colorPrimary100;

        &:hover {
          background-color: @colorPrimary200;
          border-color: @colorPrimary200;
        }
      }

      .ivu-btn-default:hover {
        color: @colorPrimary100;
        border-color: @colorPrimary100;
      }

      .ivu-btn-text:hover {
        color: @colorPrimary100;
      }
    }
  }

  .ivu-input-suffix i {
    line-height: 42px;
  }

  &.simple-search {
    .ivu-input {
      height: @lineHeight40;
      padding: 10px 14px;
      padding-right: 33px;
      color: #204394;
      background-color: #eef4ff;
      border-radius: @fillet8;
      border: 1px solid #c7d7fe;

      &::placeholder {
        color: #204394;
        opacity: 1;
      }

      &::-moz-placeholder {
        color: #204394;
        opacity: 1;
      }
  
      &:-ms-input-placeholder {
        color: #204394;
      }
    
      &:-ms-input-placeholder {
        color: #204394;
      }
  
      &::-webkit-input-placeholder {
        color: #204394;
      }
    }
  }
}
