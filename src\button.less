// 按钮
.pima-btn {
  border-radius: 8px;
  &.ivu-btn {
    padding: 0 24px;
    min-width: 74px;
    height: 40px;
    line-height: normal;
    font-size: @fontSize14;
    font-weight: 600;
    border-radius: @fillet8;

    &:focus {
      box-shadow: none;
    }

    > span {
      vertical-align: baseline;
    }
  
    &.ivu-btn-ghost {
      background-color: transparent;
    }
  
    &.ivu-btn-small {
      height: 26px;
      padding: 0 8px;
      font-size: @fontSize12;
    }
  
    &.ivu-btn-large {
      min-width: 80px;
      height: 40px;
      padding: 0 24px;
      font-weight: 600;
      font-size: 14px;
    }
  
    &.ivu-btn-success {
      &:extend(.pima-btn-success all);
    }
  
    &.ivu-btn-primary {
      &:extend(.pima-btn-primary all);
    }
  
    &.ivu-btn-default {
      &:extend(.pima-btn-default all);
    }
  
    &.ivu-btn-text {
      &:extend(.pima-btn-text all);
    }
  }
}


// 主按钮
.pima-btn-primary {
  background-color: @colorPrimary100;
  border-color: @colorPrimary100;

  &:focus {
    box-shadow: none;
  }

  &[disabled] {
    color: @colorWhite;
    background-color: #8d8e8f;
    border-color: #8d8e8f;

    &:hover {
      color: @colorWhite;
      background-color: #8d8e8f;
      border-color: #8d8e8f;
    }
  }

  &:hover:not([disabled]) {
    background-color: @colorPrimary200;
    border-color: @colorPrimary200;
  }

  &.ivu-btn-ghost {
    color: @colorPrimary100;
    background-color: @colorWhite;

    &[disabled] {
      color: @colorWhite;
      background-color: #8d8e8f;

      &:hover {
        color: @colorWhite;
        background-color: #8d8e8f;
        border-color: #8d8e8f;
      }
    }

    &:hover:not([disabled]) {
      color: @colorPrimary200;
      background-color: @colorWhite;
      border-color: @colorPrimary200;
    }
  }
}


// 默认按钮
.pima-btn-default {
  color: @colorPrimary100;
  border-color: @colorPrimary300;
  background-color: @colorPrimary700;

  &[disabled] {
    color: @colorGrey500;
    background-color: #f7f8fa;
    border-color: #e0e7ee;
  }

  &:hover:not([disabled]) {
    color: @colorPrimary100;
    border-color: @colorPrimary100;
  }

  &:focus {
    box-shadow: none;
  }

  &.ivu-btn-ghost {
    color: @colorGrey200;
    border-color: @colorGrey700;

    &:hover {
      color: @colorPrimary100;
      border-color: @colorPrimary100;

      &[disabled] {
        color: @colorGrey500;
        background-color: #f7f8fa;
        border-color: #e0e7ee;
      }
    }
  }
}

// 成功按钮
.pima-btn-success {
  background-color: @colorGreen100;
  border-color: @colorGreen100;

  &[disabled] {
    color: @colorWhite;
    border-color: @colorGreen200;
    background-color: @colorGreen200;
  }

  &:hover {
    background-color: @colorGreen200;
    border-color: @colorGreen200;
  }

  &:focus {
    box-shadow: none;
  }

  &.ivu-btn-ghost {
    color: @colorGreen100;
    border-color: @colorGreen100;

    &:hover {
      color: @colorGreen200;
      border-color: @colorGreen200;

      &[disabled] {
        color: @colorGreen200;
        background-color: @colorWhite;
        border-color: @colorGreen200;
      }
    }
  }
}


// 文本按鈕
.pima-btn-text {
  min-width: auto;
  padding-right: 0;
  padding-left: 0;
  color: @colorPrimary100;

  &:hover {
    color: @colorPrimary200;
    background-color: transparent;
  }

  &:focus {
    box-shadow: none;
  }

  &.ivu-btn-icon-only {
    padding-top: 6px;
    padding-bottom: 6px;
    font-size: @fontSize18;

    &.btn-ico-green {
      color: @colorGreen;
    }

    &.btn-ico-red {
      color: @colorRed;
    }

    i {
      line-height: 1;
      vertical-align: top;
    }
  }

  &[disabled] {
    background-color: transparent;
  }
}
