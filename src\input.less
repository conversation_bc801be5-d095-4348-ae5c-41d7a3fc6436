// Input
.pima-input {
  .ivu-input {
    display: inline-block;
    width: 100%;
    height: @lineHeight42;
    line-height: 1.5;
    padding: 4px 16px;
    padding-right: 33px;
    font-size: @fontSize14;
    border: 1px solid #bdbdbd;
    border-radius: @fillet5;
    color: #3b3c3d;
    background-color: @colorWhite;
    background-image: none;
    position: relative;
    cursor: text;
    -webkit-transition: border 0.2s ease-in-out, background 0.2s ease-in-out;
    transition: border 0.2s ease-in-out, background 0.2s ease-in-out;

    &::-moz-placeholder {
      color: @colorGrey500;
      opacity: 1;
    }

    &:-ms-input-placeholder {
      color: @colorGrey500;
    }
  
    &:-ms-input-placeholder {
      color: @colorGrey500;
    }

    &::-webkit-input-placeholder {
      color: @colorGrey500;
    }

    &:active {
      box-shadow: none;
      outline: 0;
    }
  
    &:hover:not(.ivu-input-disabled),
    &:focus:not(.ivu-input-disabled) {
      border-color: @borderHoverColor;
      box-shadow: none;
      outline: 0;
    }

    &.ivu-input-disabled {
      background-color: @disabledBackgroundColor;
      opacity: 1;
      cursor: not-allowed;

      &:hover {
        border-color: #bdbdbd;
      }
    }
  }

  .ivu-input-icon {
    height: @lineHeight42;
    line-height: @lineHeight42;
  }

  &.simple-search {
    .ivu-input {
      height: @lineHeight40;
      padding: 10px 14px;
      padding-right: 33px;
      background-color: #eef4ff;
      border-radius: @fillet8;
      border: 1px solid #c7d7fe;

      &::-moz-placeholder {
        color: fade(#204394, 20%);
        opacity: 1;
      }
  
      &:-ms-input-placeholder {
        color: fade(#204394, 20%);
      }
    
      &:-ms-input-placeholder {
        color: fade(#204394, 20%);
      }
  
      &::-webkit-input-placeholder {
        color: fade(#204394, 20%);
      }
    }
  }
}


// Input search
.pima-input-search {
  position: relative;
  z-index: 2;
  padding: 0;
  min-width: 38px;
  border-color: #c7d7fe;
  cursor: pointer;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  box-shadow: 0px 1px 2px 0px rgba(10, 13, 18, 0.05);
  background-color: #eef4ff;

  &:active,
  &:hover,
  &:focus-within {
    background-color: @colorGrey800 !important;
    border-color: @borderColor !important;

    .ivu-input {
      border-color: @borderHoverColor !important;
      box-shadow: none;
      outline: 0;
    }
  }

  .ivu-input {
    height: 40px;
    border: 1px solid #c7d7fe;
    border-radius: @fillet8;
    background-color: #eef4ff;
    padding: 10px 14px;
    padding-left: 38px;

    &::-moz-placeholder {
      color: fade(#204394, 20%);
      opacity: 1;
    }

    &:-ms-input-placeholder {
      color: fade(#204394, 20%);
    }
  
    &:-ms-input-placeholder {
      color: fade(#204394, 20%);
    }

    &::-webkit-input-placeholder {
      color: fade(#204394, 20%);
    }
  }

  .input-search-prefix {
    width: 34px;
    height: 100%;
    background-image: data-uri("../assets/img/search.png");
    background-repeat: no-repeat;
    background-position: 14px center;
    background-size: 20px;
    cursor: pointer;
  }

  .ivu-input-icon {
    height: 40px;
    line-height: 40px;
  }
}

.pima-input-search-inline {
  position: relative;
  z-index: 2;
  padding: 0;
  min-width: 38px;
  border-color: @borderColor;
  cursor: pointer;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;

  .ivu-input-search {
    border-left: none;
  }

  .ivu-input-icon {
    top: -4px;
  }

  .ivu-icon-ios-search {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 18px;
    height: 18px;
    color: @colorPrimary100;
    background: data-uri(@inputSearchIconPath) no-repeat center center / 100% auto;
    transform: translate(-50%, -50%);

    &::before {
      display: none;
    }
  }

  &:active,
  &:hover,
  &:focus-within {
    background: @colorGrey800;
    border-color: @borderColor !important;

    .ivu-input {
      border-color: @borderHoverColor;
      border-right: none;
      box-shadow: none;
      outline: 0;
    }

    .ivu-input-search {
      border-color: @borderHoverColor !important;
      border-left: none !important;
    }

    .ivu-icon-ios-search {
      color: @colorPrimary200;
      border-color: @borderColor !important;
    }
  }

  .ivu-input {
    height: 24px;
    border: 1px solid @borderColor;
    border-top-left-radius: @fillet0;
    border-bottom-left-radius: @fillet0;
    border-right: none;
  }

  .ivu-input-group {
    top: 0;
  }

  .ivu-input-group-prepend,
  .ivu-input-group-append {
    border-radius: @fillet0;
  }

  .ivu-input-search {
    background-color: #ebebebeb !important;
    border: 1px solid #ebebebeb !important;
    border-left: none;

    &::before {
      content: none;
    }

    &:active,
    &:hover,
    &:focus-within {
      background-color: @colorPrimary200 !important;
      border-color: @borderHoverColor !important;

      .ivu-icon-ios-search {
        background-image: data-uri(@inputSearchIconHoverPath);
      }
    }
  }
}

.pima-input-number {
  border: 1px solid @borderColor;
  border-radius: @fillet2;
  color: @colorGrey200;
  background-color: @colorWhite;

  &.ivu-input-number-default {
    box-shadow: none;
  }

  &:focus,
  &:focus-within,
  &:hover {
    border-color: @borderHoverColor;
    box-shadow: none;
    outline: 0;
  }
}

.pima-input-type-textarea {
  .ivu-input {
    padding: 10px 16px;
    font-size: @fontSize14;
    border: 1px solid @borderColor;
    border-radius: @fillet5;
    color: @colorGrey200;
    background-color: @colorWhite;
    background-image: none;
    cursor: text;

    &::placeholder {
      color: @colorGrey500;
    }

    &:active {
      box-shadow: none;
      outline: 0;
    }

    &:hover:not([disabled]),
    &:focus:not([disabled]) {
      border-color: @borderHoverColor;
      box-shadow: none;
      outline: 0;
    }

    &[disabled],
    &[disabled]:hover {
      background-color: @disabledBackgroundColor;
      opacity: 1;
      cursor: not-allowed;
      color: @colorGrey400;
      border-color: @borderColor;
    }
  }
}
