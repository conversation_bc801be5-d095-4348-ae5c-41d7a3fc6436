// Tree
.pima-tree {
  background-color: @colorGrey900;

  li {
    ul {
      padding-left: 0;
    }
  }

  ul {
    li {
      position: relative;
      margin: 0;
    }

    .ivu-tree-arrow {
      top: 10px;
      left: 10px;
    }

    .ivu-tree-title {
      height: 40px;
      padding-left: 36px;
      line-height: 40px;
    }

    ul {
      .ivu-tree-arrow {
        top: 7px;
        left: 28px;
      }

      .ivu-tree-title {
        height: 34px;
        padding-left: 54px;
        line-height: 34px;
        background-color: @colorGrey900;
        border-bottom: none;
      }

      ul {
        .ivu-tree-arrow {
          left: 46px;
        }

        .ivu-tree-title {
          padding-left: 72px;
        }

        ul {
          .ivu-tree-arrow {
            left: 64px;
          }

          .ivu-tree-title {
            padding-left: 90px;
          }

          ul {
            .ivu-tree-arrow {
              left: 82px;
            }

            .ivu-tree-title {
              padding-left: 108px;
            }

            ul {
              .ivu-tree-arrow {
                left: 100px;
              }

              .ivu-tree-title {
                padding-left: 126px;
              }

              ul {
                .ivu-tree-arrow {
                  left: 118px;
                }

                .ivu-tree-title {
                  padding-left: 144px;
                }

                ul {
                  .ivu-tree-arrow {
                    left: 136px;
                  }

                  .ivu-tree-title {
                    padding-left: 162px;
                  }

                  ul {
                    .ivu-tree-arrow {
                      left: 154px;
                    }

                    .ivu-tree-title {
                      padding-left: 180px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .ivu-tree-arrow {
    position: absolute;
    top: 8px;
    left: 10px;
    z-index: 2;

    .ivu-icon {
      position: relative;
      width: 8px;
      height: 8px;
      overflow: hidden;

      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: data-uri("../assets/img/arrow.png") no-repeat center center / 100% auto;
        content: " ";
      }
    }
  }

  .ivu-tree-title {
    position: relative;
    width: 100%;
    padding-top: 0;
    padding-bottom: 0;
    background-color: @colorWhite;
    border-bottom: 1px solid @borderColor;
    border-radius: 0;

    &:hover {
      background-color: #eee;
    }

    &.ivu-tree-title-selected {
      background-color: #eee;
      font-weight: 500;
      color: @colorPrimary100;

      &::before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        background-color: @colorPrimary100;
        border-radius: 0 4px 4px 0;
        content: " ";
      }
    }

    .title-wrap {
      display: inline-block;
      width: 100%;
      height: 100%;

      .title {
        display: inline-block;
        width: calc(100% - 32px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        &.highlight {
          color: @colorPrimary100;
        }

        .highlight {
          color: @colorPrimary100;
        }
      }

      .extra {
        float: right;
        margin-right: 10px;

        .more {
          position: relative;
          display: block;
          width: 20px;
          height: 12px;

          i {
            display: block;
            width: 100%;
            height: 100%;
            background: data-uri("../assets/img/more.png") no-repeat center center / auto 100%;
          }
        }

        .pima-popper {
          .ivu-poptip-content {
            .ivu-poptip-inner {
              .ivu-poptip-body {
                padding: 0;
              }
            }
          }
        }
      }
    }
  }

  .ivu-tree-empty {
    padding: 8px 16px;
    text-align: center;
  }
}

.pima-tree-wrap {
  position: relative;

  .pima-tree-search {
    padding: 8px 24px;
    border-bottom: 1px solid @borderColor;
  }
}
