// cascader
.pima-cascader {
  &.ivu-cascader {
    &.ivu-cascader-size-default {
      width: 100%;
    }

    .ivu-input {
      padding-left: 16px;
      border-radius: @fillet5;
      min-height: @lineHeight42;
      line-height: @lineHeight42;
      font-size: @fontSize14;

      &:hover {
        border-color: @colorPrimary100;
      }

      &:focus {
        border-color: @@colorPrimary100;
        box-shadow: none;
      }

      &::placeholder {
        color: #8D8E8F;
      }
    }
  }

  .ivu-select-dropdown {
    max-width: 100%;
    padding: 0;
    box-shadow: @boxShadowLevel2;
  }

  .ivu-cascader-menu {
    .ivu-cascader-menu-item {
      position: relative;
      display: flex;
      gap: 6px;
      align-items: center;
      justify-content: space-between;
      padding: 6px 10px;
      height: @lineHeight32;
      border-radius: @fillet4;
      color: @colorGrey300;
      font-size: @fontSize12;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      text-overflow: ellipsis;
      overflow: visible;
      white-space: nowrap;


      &:hover {
        color: @colorPrimary100;
      }
    }

    .ivu-icon.ivu-icon-ios-arrow-forward {
      position: relative;
      right: 0;
      top: 0;
      transform: none;
    }

    &.ivu-cascader-menu-item-active {
      color: @colorPrimary100;
      background-color: #fff;
    }

    &:hover {
      background-color: #f3f3f3;
    }
  }
}


.pima-cascader-transfer.ivu-cascader-transfer.ivu-select-dropdown {
  
}