.pima-nav-form-wrap {
  display: flex;
  gap: 70px;
  align-items: start;
  justify-content: space-between;
  margin: 0 auto;
  padding: 0 150px;
  overflow: visible;

  .pima-nav-bar {
    position: fixed;
    top: 286px;
    left: 406px;
    z-index: 1;
    min-width: 120px;
    max-width: 200px;

    .nav-title {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 50px;
      color: #fff;
      font-size: 14px;
      background: #365aa4;
    }

    .nav-item-wrap {
      .nav-item {
        position: relative;
        padding-left: 24px;
        color: #8d8e8f;
        font-weight: 600;
        font-size: 16px;
        line-height: 22px;
        cursor: pointer;
        transition: all 0.2s ease-in-out;

        .dot {
          position: absolute;
          top: 5px;
          left: 0;
          display: block;
          width: 12px;
          height: 12px;
          background-color: #ecedef;
          border-radius: 50%;
          backdrop-filter: blur(2px);
          transition: all 0.2s ease-in-out;
        }

        &:not(:last-child) {
          margin-bottom: 64px;

          &::before {
            position: absolute;
            top: 29px;
            left: 5px;
            display: block;
            width: 2px;
            height: 40px;
            background-color: #e9eaeb;
            content: "";
          }
        }

        &:hover,
        &.active {
          color: var(--theme-color);
        }

        &.active {
          .dot {
            background: var(--theme-color);
          }
        }
      }
    }
  }
}

@media screen and (max-width: @screenXlMin) {
  .pima-nav-form-wrap {
    padding: 0 20px;

    .pima-nav-bar {
      left: 316px;
    }
  }
}
