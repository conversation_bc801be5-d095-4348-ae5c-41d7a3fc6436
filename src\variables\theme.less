/** 基本色 */
// :root
@colorWhite: #fff;
@colorBlack: #000;
@colorBlue: #365aa4;
@colorGreen: #4fad4e;
@colorRed: #d44946;

// grey
@colorGrey100: #171717;
@colorGrey200: #111;
@colorGrey300: #444;
@colorGrey400: #3b3c3d;
@colorGrey500: #8d8e8f;
@colorGrey600: #dadada;
@colorGrey700: #e4e4e4;
@colorGrey800: #efefef;
@colorGrey900: #f9f9f9;

/** 功能色 */
// primary
@colorPrimary100: #7366f2;
@colorPrimary200: #6B75F8;
@colorPrimary300: #E9D7FE;
@colorPrimary400: #6589b0; // TODO:待定
@colorPrimary500: #7f9dbe; // TODO:待定
@colorPrimary600: #9ab2cb; // TODO:待定
@colorPrimary700: #F6F5FF;

// green
@colorGreen100: #009944;
@colorGreen200: #3db271;
@colorGreen300: #7bca9e;
@colorGreen400: #b8e3cb;
@colorGreen500: #f4fff9;

// gold
@colorGold100: #f49b00;
@colorGold200: #f4b655;
@colorGold300: #f4c986;
@colorGold400: #f4dcb7;
@colorGold500: #fff9ef;

// red
@colorRed100: #e63c3c;
@colorRed200: #ff5c5c;
@colorRed300: #ffa8a8;
@colorRed400: #ffc2c2;
@colorRed500: #fff5f5;

/** 文字 */
// 字重
@fontWeightNormal: normal;
@fontWeight500: 500;
@fontWeightBold: bold;

// fontSize
// 大标题
@fontSize30: 30px;
// 标题
@fontSize24: 24px;
// head头部
@fontSize18: 18px;
// Form表单/弹窗标题
@fontSize16: 16px;
// 表格文字
@fontSize15: 15px;
// 主要文本
@fontSize14: 14px;
@fontSize12: 12px;
@fontSize11: 11px;
@fontSize10: 10px;

// lineHeight
@lineHeight42: 42px;
@lineHeight40: 40px;
@lineHeight38: 38px;
@lineHeight32: 32px;
@lineHeight30: 30px;
@lineHeight26: 26px;
@lineHeight24: 24px;
@lineHeight22: 22px;
@lineHeight20: 20px;
@lineHeight19: 19px;

// fillet圆角
@fillet0: 0;
@fillet1: 1px;
@fillet2: 2px;
@fillet3: 3px;
@fillet4: 4px;
@fillet5: 5px;
@fillet8: 8px;

// 一些阴影色
@boxShadowLevel1: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
@boxShadowLevel2: 0 5px 10px -3px rgba(0, 0, 0, 0.3);
@boxShadowLevel3: 0 5px 15px -5px rgba(0, 0, 0, 0.06);

// Input
@inputSearchIconPath: "../assets/img/search.png";
@inputSearchIconHoverPath: "../assets/img/search.png";

// 边框颜色
@borderColor: #bdbdbd;
@borderHoverColor: var(--primary-color);

// Select颜色
@selectArrowPath: "../assets/img/select/select-arrow.png";
@simpleSearchSelectArrowPath: "../assets/img/select/simple-search-arrow.png";
@selectBorderColor: #E9EAEB;
@selectItemHoverColor: @colorPrimary500;
@selectItemHoverBackgroundColor: #f5f8fB;

// 文件上传组件颜色
@attachmentBackgroundHoverColor: #f5f5f5;

// 高级搜索背景颜色
@searchAdvancedBackgroundColor: fade(#f6f6f6, 50%);

// 表格
@tableEmptyImageUrl: "../assets/img/empty.png";
@tableBorderBottom: #f2f2f2;

// 表格更多操作
@tableMoreActionRadioButtonHoverBackgroundColor: #eef4ff;

// 禁止输入背景色
@disabledBackgroundColor: #fafafa;

// 滚动条色
@scrollbarThumbColor: #d8d8d8;

// 分辨率
@screenSmMin: 768px;
@screenMdMin: 992px;
@screenLgMin: 1200px;
@screenXlMin: 1680px;

// 头部
@headerHeight: 60px;
@headerBlockLogoWidth: 240px; 

// 侧边栏
@siderWidth: @headerBlockLogoWidth;
@siderSelectedBackgroundColor: #eee;

// main区域
@mainMargin: 16px;
@mainBorderRadius: 24px;

// Badge
@badgeColor: @colorRed100;

// 抽屉表单分块线颜色
@drawerFormBlockBorderColor: @colorGrey800;

// Radio
@radioBorderRadius: 50%;

// 禁用
@forbiddenImagePath: "../assets/img/forbidden.png";


/** 功能色 */
// 紫
@colorPrimaryPurple100: #7366f2;

/** 功能色 */
// 蓝
@colorPrimaryBlue100: #25a4fd;

/** 功能色 */
// 绿
@colorPrimaryGreen100: #3bd5c6;

/** 功能色 */
// 红
@colorPrimaryRed100: #ec5c54;

@import url(../default.less);