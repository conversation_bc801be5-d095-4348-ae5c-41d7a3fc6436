.pima-select-people-associative {
  width: 100%;
  height: 100%;

  .default-transtion(@property : all) {
    transition: @property 0.4s ease-in-out;
  }

  .sp-associative-option {
    padding: 0 10px;
    cursor: pointer;
    transition: background-color 0.4s ease-in-out;

    &:hover,
    &.is-checked {
      background-color: #f3f5fa;
    }

    .option-content {
      padding: 5px 0;
  
      & > * {
        margin: 0;
      }

      &:not(.is-first) {
        border-top: 1px solid #e6e6e6;
      }

      .title,
      .subTitle {
        color: fade(#000, 65%);
        font-size: 14px;
        line-height: 20px;
      }

      .sp-high-light {
        color: #8d0306;
      }
    }

    &.is-checked {
      .option-content {
        .title {
          color: #8d0306;
        }
      }
    }
  }

  .select-people-primary-border {
    border: 1px solid @borderColor;
    border-radius: @fillet5;
    .default-transtion(border-color);

    &:hover, &.active {
      border-color: @colorPrimary100;
    }
  }

  .ivu-form-item-error {
    .select-people-primary-border.in-form {
      &, &:hover, &.active {
        border-color: #ed4014;
      }
    }
  }

  .select-people-colon {
    &::after {
      content: ':';
      padding: 0 4px 0 2px;
    }
  }

  .select-people-associative-content {
    background: #fff;
    overflow-y: auto;
  }

  .select-people-associative {
    width: 100%;

    .header {
      height: 42px;
      padding: 4px 16px;
      line-height: 32px;
  
      .input-keyword {
        width: 100%;
        font-size: 14px;
        line-height: 1.5;
        color: #222;
        border-radius: 5px;
  
        &::placeholder {
          color: rgba(0,0,0,0.45);
        }
  
        &, &:focus, &:hover {
          border: none;
          outline: none;
        }
      }
    }
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: fade(#000, 6%);
    border-radius: 3px;
    box-shadow: inset 0 0 5px fade(#000, 8%);
  }
  
  ::-webkit-scrollbar-thumb {
    background: fade(#000, 12%);
    border-radius: 3px;
    box-shadow: inset 0 0 10px fade(#000, 20%);
  }
}