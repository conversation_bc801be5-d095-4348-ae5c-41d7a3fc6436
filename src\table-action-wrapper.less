.pima-table-action-wrapper {
  .ivu-btn-text {
    width: 40px;
    height: 40px;

    .icon {
      width: 40px;
    }

    &:hover:not([disabled]) {
      color: @colorPrimary100;
      background-color: @tableMoreActionRadioButtonHoverBackgroundColor !important;
    }
  }

  .ivu-poptip {
    margin-left: 10px;

    .pima-table-action-more {
      position: relative;
      display: inline-block;
      width: 20px;
      height: 20px;
      vertical-align: middle;

      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: data-uri("../assets/img/more.png") no-repeat center center / auto 14px;
        content: " ";
      }
    }
  }

  &.text-btn {
    .ivu-btn-text {
      width: auto;

      &:hover {
        background-color: unset !important;
      }
    }
  }
}
