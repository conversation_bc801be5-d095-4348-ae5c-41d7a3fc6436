.pima-action-log {
  position: fixed;
  top: 187px;
  right: 206px;
  width: 280px;
  min-height: 100px;
  overflow-y: hidden;
  background-color: #fff;
  border-radius: 16px;

  .pima-action-log-title {
    padding: 16px 20px;
    color: var(--primary-color);
    font-weight: 500;
    font-size: 16px;
    border-bottom: 1px solid #ededed;
    border-radius: 16px 16px 0 0;
  }

  .pima-action-log-body {
    height: 100%;
    max-height: calc(100vh - 72px - 36px - 187px - 54px);
    padding: 20px 22px;
    overflow-y: auto;
  }

  .ivu-steps-title {
    margin-bottom: 6px;
    color: #3b3c3d;
    font-weight: 400;
    font-size: 15px;
  }

  .ivu-steps-vertical {
    .ivu-steps-tail {
      left: 9px;

      & > i::after {
        background-color: #ededed;
      }
    }
  }

  .ivu-steps-item {
    &.ivu-steps-status-finish .ivu-steps-title {
      color: #3b3c3d;
    }
  }

  .name-action,
  .remark {
    margin-bottom: 6px;
    color: #8d8e8f;
    font-weight: 400;
    font-size: 14px;
  }

  .action-time {
    margin-bottom: 6px;
    color: #646566;
    font-weight: 500;
    font-size: 14px;
  }
}

@media screen and (max-width: @screenXlMin) {
  .pima-action-log {
    right: 36px;
  }
}
