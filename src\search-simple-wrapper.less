// Search-Simple
.pima-search-simple-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 50px;
  padding: 0 24px;

  .menu-title {
    flex-shrink: 0;
    margin-right: 24px;
    color: #3b3c3d;
    font-weight: 600;
    font-size: 18px;
    line-height: 40px;
  }

  > .left,
  > .right {
    display: flex;
  }

  > .left {
    align-items: flex-start;
    flex-wrap: wrap;
    padding-top: 20px;

    & > * {
      margin-bottom: 20px;
    }

    .pima-btn.ivu-btn.ivu-btn-text {
      color: #8d8e8f;

      &:hover {
        color: @colorPrimary100;
      }
    }

    .pima-btn.ivu-btn {
      border-radius: @fillet8;
    }
  }
}
