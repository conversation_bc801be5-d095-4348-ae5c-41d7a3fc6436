.pima-app-sider {
  padding: 8px;

  > ul.menu {
    font-size: @fontSize16;
    background: transparent;
    border-right: none;

    > li {
      position: relative;
      height: auto;
      margin: 0;
      overflow: hidden;
      line-height: 1;
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;

      &:not(:last-child) {
        margin-bottom: 4px;
      }

      a {
        position: relative;
        display: inline-block;
        width: 100%;
        height: 42px;
        padding: 11px 14px;
        color: #3b3c3d;
        font-weight: 500;
        font-size: 16px;
        text-decoration: none;
        border-radius: 8px;

        .icon {
          vertical-align: middle;
        }

        .title {
          display: inline-block;
          max-width: calc(100% - 46px);
          line-height: 20px;
          text-overflow: ellipsis;
          vertical-align: middle;
          overflow: hidden;
        }

        .badge {
          position: relative;
          display: inline-block;
          box-sizing: border-box;
          margin: 0;
          padding: 0;
          color: unset;
          vertical-align: middle;
          list-style: none;

          .badge-count {
            position: relative;
            top: auto;
            display: block;
            min-width: 16px;
            height: 16px;
            padding: 0 8px;
            overflow: hidden;
            color: @colorWhite;
            font-size: @fontSize12;
            line-height: 16px;
            white-space: nowrap;
            background: @badgeColor;
            border-radius: 10px;
            transform: none;
          }
        }
      }

      ul.submenu {
        position: relative;
        max-height: 0;
        margin-top: 4px;
        padding-left: 34px;
        font-size: 14px;
        overflow: hidden;
        transition: all .2s ease-in-out;
        transition-delay: 0s;
        opacity: 0;
    

        &::before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 22px;
          display: block;
          width: 1px;
          height: 100%;
          background-color: #e9eaeb;
          content: "";
        }

        li {
          &:not(:last-child) {
            margin-bottom: 4px;
          }

          a {
            height: 42px;
            padding: 11px 14px;
            color: #8d8e8f;
            font-weight: 400;
            font-size: 14px;
            background-color: transparent;

            &::after {
              display: none;
            }
          }

          &.selected {
            a {
              color: #fff;
              font-weight: 500;
              background-color: var(--theme-color);
            }
          }
        }
      }

      &.selected {
        position: relative;

        a {
          color: #fff;
          font-weight: @fontWeight500;
          background-color: var(--theme-color);
        }

        &.with-child a {
          color: #3b3c3d;
          font-weight: 500;
          background-color: unset;
        }
      }

      &.with-child a {
        position: relative;

        &::after {
          position: absolute;
          top: 11px;
          right: 8px;
          display: block;
          width: 19px;
          height: 19px;
          background: data-uri("../assets/img/menu-arrow.svg") no-repeat;
          transition: all 0.2s linear;
          content: "";
        }
      }

      &.open {
        ul.submenu {
          max-height: 10000px;
          opacity: 1;
        }

        &.with-child a::after {
          transform-origin: 50% 50%;
          transform: rotate(-180deg);
        }
      }
    }
  }
}
