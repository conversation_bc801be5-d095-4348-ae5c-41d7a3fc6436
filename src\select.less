// Select
.pima-select {
  &.ivu-select {
    .ivu-select-placeholder {
      padding-left: 16px;
      color: #8D8E8F;
      font-size: @fontSize15;
      height: @lineHeight42;
      line-height: @lineHeight42;
    }

    .ivu-icon-ios-arrow-down {
      width: 14px;
      height: 14px;
      background: data-uri(@selectArrowPath) no-repeat center center;
      background-size: 13px 7px;

      &::before {
        display: none;
      }
    }

    .ivu-select-selected-value {
      padding-left: 16px;
      height: @lineHeight42;
      line-height: @lineHeight42;
      color: @colorGrey200;
      font-size: @fontSize14;
    }

    .ivu-select-item {
      color: #494a4b;
      font-size: @fontSize14;
  
      &:hover {
        color: #3b3c3d;
        background-color: @selectItemHoverBackgroundColor;
      }
    }

    .ivu-select-item-selected,
    .ivu-select-item-focus {
      color: @colorPrimary100;
      background: none;
    }

    .ivu-select-input {
      height: @lineHeight40;
      line-height: @lineHeight40;
      font-size: @fontSize14;

      &::placeholder {
        color: @colorGrey500;
      }
    }

    .ivu-select-dropdown {
      max-width: 100%;
      padding: 0;
      box-shadow: @boxShadowLevel2;
    }

    .ivu-select-selection {
      min-height: @lineHeight42;
      border: 1px solid @borderColor;
      border-radius: @fillet5;

      &:hover {
        border-color: @borderHoverColor;

        .ivu-select-arrow::before {
          color: @colorPrimary100;
        }
      }
    }
    &.ivu-select-visible {
      .ivu-select-selection {
        box-shadow: none;
        border-color: @borderHoverColor;
      }
    }

    &.ivu-select-disabled {
      .ivu-select-selection {
        background-color: @disabledBackgroundColor;
  
        .ivu-select-selected-value {
          color: @colorGrey400;
        }

        &:hover {
          border-color: @borderColor;
        }
      }
    }

    .ivu-select-not-found {
      padding-top: 8px;
    }

    .ivu-select-dropdown-list {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 6px;

      .ivu-select-item {
        position: relative;
        display: flex;
        align-items: center;
        padding: 6px 10px;
        height: 32px;
        border-radius: 4px;
        color: @colorGrey300;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        text-overflow: ellipsis;
        overflow: visible;
        white-space: nowrap;
      }

      .ivu-select-item-selected {
        color: @colorPrimary100;
      }
    }

    &.ivu-select-multiple {
      .ivu-select-item-selected {
        &::after {
          color: @colorPrimary100;
        }
      }
    }

    &.ivu-select-multiple {
      .ivu-select-dropdown-list {
        .ivu-select-item {
          padding-left: 32px;

          &::after {
            position: absolute;
            left: 8px;
            top: 50%;
            width: 16px;
            height: 16px;
            color: @colorWhite;
            font-size: 16px;
            background: @colorWhite;
            border: 1px solid #dcdcdc;
            border-radius: 3px;
            transform: translateY(-50%);
            content: "";
          }
        }

        .ivu-select-item-selected {
          &::before {
            z-index: 1;
            position: absolute;
            left: 8px;
            top: 50%;
            width: 16px;
            height: 16px;
            background-image: data-uri("../assets/img/selected.svg");
            background-repeat: no-repeat;
            background-position: center center;
            transform: translateY(-50%);
            content: "";
          }

          &::after {
            background: @colorPrimary100;
            border: 2px solid @borderHoverColor;
          }
        }
      }
    }
  }

  // 简易搜索样式
  &.simple-search.ivu-select {
    .ivu-select-placeholder,
    .ivu-select-selected-value {
      padding-right: 38px;
      padding-left: 14px;
      color: #204394;
      font-size: @fontSize14;
      height: @lineHeight40;
      line-height: @lineHeight40;
    }

    .ivu-icon-ios-arrow-down {
      width: 20px;
      height: 20px;
      background: data-uri(@simpleSearchSelectArrowPath) no-repeat center center;
      background-size: 20px 20px;
    }

    .ivu-select-selection {
      min-height: @lineHeight40;
      border: 1px solid #c7d7fe;
      border-radius: @fillet8;
      background-color: #eef4ff;
      box-shadow: 0 1px 2px 0 rgba(10, 13, 18, 0.05);

      &:hover {
        border-color: @borderHoverColor;
      }
    }
  }
}
