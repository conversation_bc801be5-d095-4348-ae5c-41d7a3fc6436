.pima-root {
  --theme-color: @colorPrimaryPurple100;
  --primary-color: @colorPrimary100;
  --primary-hover-color: @colorPrimary200;
  --primary-disabled-color: @colorPrimary300;
  --primary-text-color: @colorGrey300;
  --primary-color-100: @colorPrimary100;
  --primary-color-200: @colorPrimary200;
  --primary-color-300: @colorPrimary300;
  --primary-color-400: @colorPrimary400;
  --primary-color-500: @colorPrimary500;
  --primary-color-600: @colorPrimary600;
  --primary-color-700: @colorPrimary700;
  --secondary-text-color: @colorGrey400;
  --secondary-hover-color: @colorPrimary500;
  --border-color: @borderColor;
  --blue-color: @colorBlue;
  --green-color: @colorGreen100;
  --green-color-100: @colorGreen100;
  --green-color-200: @colorGreen200;
  --green-color-300: @colorGreen300;
  --green-color-400: @colorGreen400;
  --green-color-500: @colorGreen500;
  --yellow-color: @colorGold100;
  --yellow-color-100: @colorGold100;
  --yellow-color-200: @colorGold200;
  --yellow-color-300: @colorGold300;
  --yellow-color-400: @colorGold400;
  --yellow-color-500: @colorGold500;
  --red-color: @colorRed100;
  --red-color-100: @colorRed100;
  --red-color-200: @colorRed200;
  --red-color-300: @colorRed300;
  --red-color-400: @colorRed400;
  --red-color-500: @colorRed500;
  --grey-color: @colorGrey300;
  --grey-color-100: @colorGrey100;
  --grey-color-200: @colorGrey200;
  --grey-color-300: @colorGrey300;
  --grey-color-400: @colorGrey400;
  --grey-color-500: @colorGrey500;
  --grey-color-600: @colorGrey600;
  --grey-color-700: @colorGrey700;
  --grey-color-800: @colorGrey800;
  --grey-color-900: @colorGrey900;
  --header-height: @headerHeight;

  // 字體和一期項目保持一致
  font-family: "PingFang SC", "Microsoft YaHei", "微软雅黑", "Hiragino Sans GB", "Helvetica Neue", Helvetica, Tahoma, Arial, sans-serif;
  font-size: @fontSize14;
  word-wrap: break-word;
  word-break: break-word;
}

.pima-theme-blue {
  --theme-color: @colorPrimaryBlue100;
}

.pima-theme-green {
  --theme-color: @colorPrimaryGreen100;
}

.pima-theme-red {
  --theme-color: @colorPrimaryRed100;
}
