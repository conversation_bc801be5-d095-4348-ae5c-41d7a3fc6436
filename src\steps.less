.pima-steps-dot {
  &.ivu-steps-vertical {
    .ivu-steps-tail {
      left: 6px;

      > i::after {
        display: none;
      }
    }

    .ivu-steps-head-inner {
      width: 12px;
      margin-right: 0;
      background-color: transparent;
      border-color: transparent;

      > .ivu-steps-icon {
        position: relative;
        vertical-align: baseline;

        &::before {
          display: block;
          width: 10px;
          height: 10px;
          overflow: hidden;
          background-color: #4CAF4B;
          border-radius: 50%;
          content: " ";
        }
        &::after {
          position: absolute;
          left: 50%;
          top: 50%;
          display: block;
          width: 15px;
          height: 15px;
          overflow: hidden;
          background-color: #4CAF4B;
          opacity: 0.2;
          border-radius: 50%;
          content: " ";
          transform: translate(-50%, -50%);
        }
      }
    }

    .ivu-steps-main {
      padding-left: 10px;

      .ivu-steps-title {
        color: @colorGrey200;
        font-weight: @fontWeightNormal;
        font-size: @fontSize16;
      }

      .ivu-steps-content {
        color: @colorGrey300;
        font-size: @fontSize14;
      }
    }
  }
}

// 详情流程图
.pima-detail-process {
  .ivu-steps-icon > img {
    width: 32px;
    height: 32px;
  }

  &.ivu-steps-vertical .ivu-steps-tail {
    left: 18px;

    > i::after {
      background: #e9eaeb !important;
    }
  }

  .ivu-steps-title {
    width: 100%;
  }

  .ivu-steps-content {
    margin-top: 12px;
    margin-bottom: 40px;
  }

  .title-box {
    display: flex;
    gap: 10px;
    align-items: center;
  
    .process-time {
      color: #494a4b;
      font-weight: 500;
      font-size: 20px;
    }
  }

  .process-content {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: space-between;
    padding: 24px;
    background-color: #f5f8fb;
    border-radius: 16px;
  
    .left-meta {
      .title {
        display: flex;
        gap: 8px;
        color: #3b3c3d;
        font-weight: 500;
        font-size: 18px;
      }
  
      .meta {
        display: flex;
        align-items: center;
        margin-top: 8px;
  
        .item {
          position: relative;
          margin-right: 8px;
          padding-right: 8px;
          color: #8d8e8f;
          font-weight: 400;
          font-size: 14px;
  
          &:not(:last-child)::after {
            position: absolute;
            top: 50%;
            right: 0;
            bottom: 0;
            display: block;
            width: 1px;
            height: 60%;
            background-color: #8d8e8f;
            transform: translateY(-50%);
            content: "";
          }
        }
      }
    }
  
    .right-actions {
      display: flex;
      gap: 12px;
      align-items: center;
  
      .action-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 52px;
        height: 52px;
        background-color: #fff;
        border: 1px solid #fff;
        border-radius: 50%;
        cursor: pointer;
        transition: all .2s linear;
  
        img {
          width: 40px;
          height: 40px;
        }
  
        &:hover:not(.disabled) {
          border-color: var(--primary-color-300);
        }

        &.disabled {
          cursor: not-allowed;
        }
      }
    }
  }
}
