.pima-layout {
  background-color: @colorGrey900;

  > .header {
    position: fixed;
    z-index: 99;
    left: 0;
    top: 0;
    right: 0;
    height: @headerHeight;
    background-color: @colorWhite;
  }

  > .body {
    display: flex;
    overflow: auto;
    width: 100%;
    padding-top: @headerHeight;
    min-height: 100vh;
    background: linear-gradient(266deg, rgba(189, 219, 243, 0.70) 22.03%, rgba(225, 237, 255, 0.70) 84%), #FFF;

    &:not(.body-has-sider) {
      > .sider {
        display: none;
      }
    }

    > .sider {
      overflow: auto;
      flex-shrink: 0;
      width: @siderWidth;
      min-width: @siderWidth;
      max-width: @siderWidth;
      height: calc(100vh - @headerHeight);
      border-right: 1px solid @colorGrey800;
      overflow: hidden auto;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.65) -16.75%, rgba(255, 255, 255, 0.40) 100%), rgba(255, 255, 255, 0.10);
    }

    > .main {
      position: relative;
      width: 100%;
      height: calc(100vh - @headerHeight);
      
      .main-body {
        height: calc(100% - @mainMargin - @mainMargin);
        margin: @mainMargin;
        border-radius: @mainBorderRadius;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.65) -16.75%, rgba(255, 255, 255, 0.40) 100%), rgba(255, 255, 255, 0.10);
        overflow: hidden auto;
      }
    }
  }
}
