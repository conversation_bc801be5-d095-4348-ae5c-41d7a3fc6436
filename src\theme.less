// @import "./variables.less";

@import (reference) "./root.less";
@import "./button.less";
@import "./select.less";
@import "./form-wrapper.less";
@import "./wrapper-form-title.less";
@import "./form-page.less";
@import "./form-block-wrapper.less";
@import "./date-picker.less";
@import "./pair-label-item.less";
@import "./select-people-tree.less";
@import "./select-input-people-search.less";
@import "./select-people.less";
@import "./select-department.less";
@import "./select-role.less";
@import "./select-people-associative.less";
@import "./tabs.less";
@import "./cascader.less";
@import "./nav-form-wrap.less";
@import "./action-log.less";
@import "./card-tabs.less";
@import (reference) "./input.less";
@import (reference) "./radio.less";
@import (reference) "./checkbox.less";
@import (reference) "./table.less";
@import (reference) "./form.less";
@import (reference) "./tooltip.less";
@import (reference) "./spin.less";
@import (reference) "./dropdown-menu.less";
@import (reference) "./popper.less";
@import (reference) "./tree.less";
@import (reference) "./tag.less";
@import (reference) "./list.less";
@import (reference) "./card.less";
@import (reference) "./steps.less";
@import (reference) "./switch.less";
@import (reference) "./app-header.less";
@import (reference) "./app-header-application-panel.less";
@import (reference) "./app-sider.less";
@import (reference) "./import.less";
@import (reference) "./forbidden.less";
@import (reference) "./button-text.less";
@import (reference) "./button-go-back.less";
@import (reference) "./button-reset.less";
@import (reference) "./button-magnifier.less";
@import (reference) "./button-remove.less";
@import (reference) "./title-bar.less";
@import (reference) "./search-simple-wrapper.less";
@import (reference) "./search-advanced-wrapper.less";
@import (reference) "./table-top-bar.less";
@import (reference) "./table-scroll.less";
@import (reference) "./table-action-wrapper.less";
@import (reference) "./table-no-data-text-wrapper.less";
@import (reference) "./paginator-wrapper.less";
@import (reference) "./upload-files-wrapper.less";
@import (reference) "./main-wrapper.less";
@import (reference) "./detail-wrapper.less";
@import (reference) "./split.less";
@import (reference) "./status.less";
@import (reference) "./content-block.less";

.pima-theme {
  &:extend(.pima-root all);

  &.pima-theme-red {
    &:extend(.pima-theme-red all);
  }

  &.pima-theme-blue {
    &:extend(.pima-theme-blue all);
  }

  &.pima-theme-green {
    &:extend(.pima-theme-green all);
  }

  // 按钮
  .pima-btn {
    &:extend(.pima-btn all);
  }

  // Input
  .pima-input {
    &:extend(.pima-input all);
  }

  // Input-Search
  .pima-input-search {
    &:extend(.pima-input-search all);
  }

  .pima-input-search-inline {
    &:extend(.pima-input-search-inline all);
  }

  // Input-Number
  .pima-input-number {
    &:extend(.pima-input-number all);
  }

  // Input-textarea
  .pima-input-type-textarea {
    &:extend(.pima-input-type-textarea all);
  }

  // Date-Picker
  .pima-date-picker {
    &:extend(.pima-date-picker all);
  }

  // Radio
  .pima-radio-default {
    &:extend(.pima-radio-default all);
  }

  .pima-radio-group-vertical {
    &:extend(.pima-radio-group-vertical all);
  }

  // Checkbox
  .pima-checkbox-group {
    &:extend(.pima-checkbox-group all);
  }

  // Select
  .pima-select {
    &:extend(.pima-select all);
  }

  // Table
  .pima-table-wrapper {
    &:extend(.pima-table-wrapper all);
  }

  // Form
  .pima-form {
    &:extend(.pima-form all);
  }

  .pima-form-item-error {
    &:extend(.pima-form-item-error all);
  }

  .pima-form-page {
    &:extend(.pima-form-page all);
  }

  .pima-form-wrapper {
    &:extend(.pima-form-wrapper all);
  }

  .pima-wrapper-form-title {
    &:extend(.pima-wrapper-form-title all);
  }

  .pima-form-block-wrapper {
    &:extend(.pima-form-block-wrapper all);
  }

  .pima-nav-form-wrap {
    &:extend(.pima-nav-form-wrap all);
  }

  .pima-content-block {
    &:extend(.pima-content-block all);
  }

  // card类型tabs
  .pima-card-tab {
    &:extend(.pima-card-tab all);
  }

  // 审批步骤
  .pima-action-log {
    &:extend(.pima-action-log all);
  }

  // Spin
  .pima-spin {
    &:extend(.pima-spin all);
  }

  // Dropdown menu
  .pima-dropdown-menu {
    &:extend(.pima-dropdown-menu all);
  }

  // Popper
  .pima-popper {
    &:extend(.pima-popper all);
  }

  // Tooltip
  .pima-tooltip {
    &:extend(.pima-tooltip all);
  }

  // Tree
  .pima-tree {
    &:extend(.pima-tree all);
  }

  .pima-tree-wrap {
    &:extend(.pima-tree-wrap all);
  }

  // Tree（选人组件）
  .pima-select-people-tree {
    &:extend(.pima-select-people-tree all);
  }

  .pima-select-people-tree-filter {
    &:extend(.pima-select-people-tree-filter all);
  }

  // 选人组件搜索输入框
  .pima-select-input-people-search {
    &:extend(.pima-select-input-people-search all);
  }

  // 选人组件
  .pima-select-people {
    &:extend(.pima-select-people all);
  }

  // 选部门组件
  .pima-select-department {
    &:extend(.pima-select-department all);
  }

  // 选择角色
  .pima-select-role {
    &:extend(.pima-select-department all);
  }

  // 联想选人组件
  .pima-select-people-associative {
    &:extend(.pima-select-people-associative all);
  }

  // Tag
  .pima-tag {
    &:extend(.pima-tag all);
  }

  // List
  .pima-list {
    &:extend(.pima-list all);
  }

  // Card
  .pima-card {
    &:extend(.pima-card all);
  }

  // Steps
  .pima-steps-dot {
    &:extend(.pima-steps-dot all);
  }

  .pima-detail-process {
    &:extend(.pima-detail-process all);
  }

  // Switch
  .pima-switch {
    &:extend(.pima-switch all);
  }


  // AppHeader
  .pima-app-header {
    &:extend(.pima-app-header all);
  }

  .pimaru-application-panel {
    &:extend(.pimaru-application-panel all);
  }

  // AppSider
  .pima-app-sider {
    &:extend(.pima-app-sider all);
  }

  // Import
  .pima-import {
    &:extend(.pima-import all);
  }

  // Forbidden
  .pima-forbidden {
    &:extend(.pima-forbidden all);
  }

  // ButtonText
  .pima-button-text {
    &:extend(.pima-button-text all);
  }

  // BottonGoBack
  .pima-button-go-back {
    &:extend(.pima-button-go-back all);
  }

  // BottonReset
  .pima-button-reset {
    &:extend(.pima-button-reset all);
  }

  // BottonMagnifier
  .pima-button-magnifier {
    &:extend(.pima-button-magnifier all);
  }

  // BottonRemove
  .pima-button-remove {
    &:extend(.pima-button-remove all);
  }

  // TitleBar
  .pima-title-bar {
    &:extend(.pima-title-bar all);
  }

  // Search-Simple
  .pima-search-simple-wrapper {
    &:extend(.pima-search-simple-wrapper all);
  }

  // Search-Advanced
  .pima-search-advanced-wrapper {
    &:extend(.pima-search-advanced-wrapper all);
  }

  .pima-pair-label-item {
    &:extend(.pima-pair-label-item all);
  }

  // TableTopBar
  .pima-table-top-bar {
    &:extend(.pima-table-top-bar all);
  }

  // TableScroll
  .pima-table-scroll {
    &:extend(.pima-table-scroll all);
  }

  // 表格操作
  .pima-table-action-wrapper {
    &:extend(.pima-table-action-wrapper all);
  }

  // 表格空数据
  .pima-table-no-data-text-wrapper {
    &:extend(.pima-table-no-data-text-wrapper all);
  }

  // 分页器
  .pima-paginator-wrapper {
    &:extend(.pima-paginator-wrapper all);
  }

  // 上传组件
  .pima-upload-files-wrapper {
    &:extend(.pima-upload-files-wrapper all);
  }

  // 主内容
  .pima-main-wrapper {
    &:extend(.pima-main-wrapper all);
  }

  // 详情内容
  .pima-detail-wrapper {
    &:extend(.pima-detail-wrapper all);
  }

  // 分隔
  .pima-split {
    &:extend(.pima-split all);
  }

  // 分隔
  .pima-status {
    &:extend(.pima-status all);
  }

  // 繁体样式
  // &.pima-chn-lang-theme {

  // }

  // 英文样式
  &.pima-eng-lang-theme {
    .pima-search-advanced-wrapper {
      &:extend(.pima-eng-search-advanced-wrapper all);
    }
  }
}
