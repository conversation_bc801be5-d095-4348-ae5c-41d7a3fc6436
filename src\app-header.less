.pima-app-header {
  > .pima-menu-bar {
    position: relative;
    z-index: 199;
    display: flex;
    justify-content: flex-start;
    height: @headerHeight;
    padding: 0;
    background: linear-gradient(130deg, #6B75F8 -8.19%, #5464F6 26.16%, #4276F4 26.18%, #3A7DFD 95.72%, #5C95FC 95.78%, #5792FA 120.07%);

    > .pima-block-logo {
      width: @headerBlockLogoWidth;
      height: 100%;
      line-height: calc(@headerHeight - 10px);

      a {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: @colorWhite;
        font-size: @fontSize12;
        line-height: calc(@headerHeight - 10px);
        text-decoration: none;
      }

      img {
        height: 100%;
        float: left;
      }

      .title {
        margin-left: 8px;
      }

      .env-tag {
        display: none;
        margin-left: 8px;
        font-weight: @fontWeightBold;
      }
    }

    > .pima-block-menu {
      display: flex;
      flex-grow: 1;
      justify-content: flex-start;
      height: 100%;
      margin: 0;
      padding: 10px 0;
      overflow: hidden;
      font-weight: 400;
      font-size: @fontSize16;
      list-style: none;
      background: none;
      border-bottom: none;

      li {
        position: relative;
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        white-space: nowrap;
        text-align: center;
        border-radius: 6px;

        &::before {
          display: none;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: @colorWhite;
          opacity: 0.1;
          content: "";
        }

        &:hover::before,
        &.selected::before {
          display: block;
        }

        a {
          display: block;
          min-width: 80px;
          padding: 0 24px;
          color: @colorWhite;
          text-decoration: none;
          line-height: 40px;

          span {
            position: relative;
          }

          &.badge span::after {
            position: absolute;
            top: -3px;
            right: -5px;
            width: 5px;
            height: 5px;
            background-color: @badgeColor;
            border-radius: 100%;
            box-shadow: #000 0 #fff;
            content: " ";
          }
        }
      }
    }

    > .pima-block-action {
      display: flex;
      align-items: center;
      height: 100%;
      padding-right: 4px;

      .pima-action {
        position: relative;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        height: 100%;
        padding: 6px 0;
        color: @colorWhite;
        cursor: pointer;

        a {
          position: relative;
          padding: 19px;
          color: @colorWhite;

          &::before {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: @colorWhite;
            border-radius: 3px;
            opacity: 0.1;
            content: "";
          }

          &:hover {
            &::before {
              display: block;
            }
          }
        }

        &.pima-icon-action {
          .action-icon {
            position: relative;
            display: block;
            width: 22px;
            height: 22px;

            &::before {
              position: absolute;
              top: 0;
              right: 0;
              width: 100%;
              height: 100%;
              background-repeat: no-repeat;
              background-size: 100% 100%;
              background-position: top left;
              content: " ";
            }
          }
        }

        &.pima-action-locale {
          a.locale-text {
            width: 60px;
            height: 60px;
            color: @colorWhite;
            font-weight: 400;
            font-size: @fontSize16;
            text-align: center;
          }

          .locale-tooltip {
            z-index: 100;
            padding-top: 6px;

            .tooltip-content {
              position: relative;

              &.tooltip-content::before {
                position: absolute;
                top: -2.5px;
                right: 16px;
                z-index: -1;
                width: 5px;
                height: 5px;
                background: #fff;
                transform: rotate(45deg);
                content: " ";
              }

              ul.locale-dropdown-menu {
                margin: 0;
                padding: 0;
                overflow: hidden;
                color: #233149;
                font-size: 12px;
                list-style: none;
                background-color: #fff;
                border-radius: 7px;
                box-shadow: 0 2px 8px lighten(#000, 85%);

                li {
                  position: relative;
                  height: 38px;
                  padding: 0 20px;
                  line-height: 38px;
                  white-space: nowrap;

                  &::after {
                    position: absolute;
                    right: 7px;
                    bottom: 0;
                    left: 7px;
                    height: 1px;
                    background-color: #94afca;
                    opacity: 0.12;
                    content: " ";
                  }

                  &:last-child::after {
                    display: none;
                  }
                }
              }
            }
          }
        }

        &.pima-action-user {
          a.user-info {
            display: flex;
            align-items: center;
            height: 60px;
            line-height: 60px;
            padding: 0 20px;

            .avatar {
              width: 36px;
              height: 36px;
              margin-right: 12px;
              border-radius: 50%;
            }

            .avatar-name {
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: @colorPrimary200;
              border: 1px solid fade(@colorWhite, 20%);
              width: 36px;
              height: 36px;
              margin-right: 12px;
              border-radius: 50%;
            }

            .name {
              display: flex;
              flex-direction: column;
              justify-content: center;
              color: @colorWhite;
              font-weight: 600;
              font-size: @fontSize14;
              line-height: 1.4;
        
              .user-name {
                display: block;
                max-width: 0;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                transition: max-width 0.3s ease-out;
              }

              .user-account {
                display: block;
              }
            }

            &.show-user-name {
              .user-name {
                max-width: 100px;
                transition: max-width 0.5s linear;
              }
            }
          }

          .user-tooltip {
            z-index: 100;
            padding-top: 6px;
        
            .tooltip-content {
              position: relative;
        
              &.tooltip-content::before {
                position: absolute;
                top: -2.5px;
                right: 16px;
                z-index: -1;
                width: 5px;
                height: 5px;
                background: @colorWhite;
                transform: rotate(45deg);
                content: " ";
              }

              ul.user-dropdown-menu {
                margin: 0;
                padding: 0;
                overflow: hidden;
                color: #233149;
                font-size: 12px;
                list-style: none;
                background-color: @colorWhite;
                border-radius: 7px;
                box-shadow: 0 2px 8px lighten(#000, 85%);

                li {
                  position: relative;
                  height: 38px;
                  padding: 0 20px;
                  line-height: 38px;
                  white-space: nowrap;
        
                  &::after {
                    position: absolute;
                    right: 7px;
                    bottom: 0;
                    left: 7px;
                    height: 1px;
                    background-color: #94afca;
                    opacity: 0.12;
                    content: " ";
                  }
        
                  &:last-child::after {
                    display: none;
                  }
                }
              }
            }
          }
        }

        &.pima-action-logout {
          &::before {
            display: none;
          }
        }
      }
    }
  }

  .message-action-icon::before {
    background-image: data-uri("../assets/img/header-action-message.png");
  }

  .help-action-icon::before {
    background-image: data-uri("../assets/img/header-action-help.png");
  }
}


