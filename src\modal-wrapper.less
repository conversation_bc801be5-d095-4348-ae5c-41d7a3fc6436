@import "./theme.less";

.pima-modal-wrapper {
  &:extend(.pima-theme all);
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;

    .ivu-modal-header {
      padding: 24px 24px 16px;
    }

    .ivu-modal-body {
      position: relative;
      max-height: calc(100vh - 32px - 65px - 73px);
      padding: 25px;
      overflow-y: auto;
    }

    .ivu-modal-content {
      border-radius: 16px;

      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      ::-webkit-scrollbar-track {
        background: fade(#000, 6%);
        border-radius: 3px;
        box-shadow: inset 0 0 5px fade(#000, 8%);
      }

      ::-webkit-scrollbar-thumb {
        background: fade(#000, 12%);
        border-radius: 3px;
        box-shadow: inset 0 0 10px fade(#000, 20%);
      }

      .ivu-modal-header-inner {
        font-weight: 700;
        font-size: @fontSize18;
      }

      .ivu-modal-close {
        right: 16px;
        top: 16px;
        z-index: 10;
      }

      .ivu-modal-close .ivu-icon-ios-close:hover {
        color: @colorPrimary100;
      }
    }

    .ivu-modal-footer {
      padding: 16px;
      text-align: center;

      button + button {
        margin-left: 16px;
      }
    }
  }

  &.pima-student-modal-wrapper {
    --primary-color: @colorGreen;
    --primary-hover-color: @colorGreen200;
    --primary-disabled-color: @colorGreen300;
  }
}

.pima-modal-hint-wrapper {
  .ivu-modal-body {
    padding: 32px 16px 27px 32px;
  }

  .pima-modal-hint-title {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 700;
    font-size: @fontSize16;

    .pima-modal-hint-warning-icon {
      width: 24px;
      height: 24px;
      margin-right: 16px;
      background: data-uri("../assets/img/warning.png");
      background-size: 100%;
      background-repeat: no-repeat;
    }
  }

  .pima-modal-hint-content {
    padding-top: 12px;
    padding-left: 40px;
  }
}

.pima-modal-confirm-wrapper {
  &:extend(.pima-theme all);

  .ivu-modal {
    .ivu-modal-header {
      padding: 14px 24px;
    }

    .ivu-modal-body {
      position: relative;
      max-height: 500px;
      padding: 34px 34px 0;
      overflow-y: auto;
    }

    .ivu-modal-content {
      border-radius: @fillet2;
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      ::-webkit-scrollbar-track {
        background: fade(#000, 6%);
        border-radius: 3px;
        box-shadow: inset 0 0 5px fade(#000, 8%);
      }
      ::-webkit-scrollbar-thumb {
        background: fade(#000, 12%);
        border-radius: 3px;
        box-shadow: inset 0 0 10px fade(#000, 20%);
      }

      .ivu-modal-header-inner {
        font-weight: 700;
        font-size: @fontSize16;
      }

      .ivu-modal-close {
        z-index: 10;
      }

      .ivu-modal-close .ivu-icon-ios-close:hover {
        color: var(--primary-color);
      }
    }

    .ivu-modal-footer {
      padding: 24px 32px;
      border-top: none;
    }
  }

  &.pima-student-modal-confirm-wrapper {
    --primary-color: @colorGreen;
    --primary-hover-color: @colorGreen200;
    --primary-disabled-color: @colorGreen300;
  }
}

// 模态窗表单
.pima-modal-form {
  width: 1300px !important;
  max-width: calc(100vw - 32px);

  .modal-form-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 1162px;
    margin: 0 auto;
    
    &.with-action-log {
      padding-right: 312px;
    }
  }

  .pima-wrapper-form-title {
    margin: 0;
    border: 1px solid #ededed;
  }

  .pima-action-log {
    top: 142px;
    right: calc((100% - 1162px) / 2);
    border: 1px solid #ededed;
  }
}

.pima-modal-form-wrapper.pima-modal-wrapper .ivu-modal .ivu-modal-body {
  height: calc(100vh - 170px);
}

@media screen and (max-width: 1220px) {
  .pima-modal-form {
    .pima-action-log {
      right: 41px;
    }
  }
}
