.pima-select-role {
  .ivu-modal {
    .ivu-modal-content {
      .ivu-modal-body {
        max-height: none;
        padding: 0;
      }
    }
  }

  .input-search {
    width: 200px;
    margin-left: 12px;
  }

  .clean-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 40px;
    box-shadow: 0 2px 12px 0 fade(#000, 7%);
    cursor: pointer;
  
    .icon-clean {
      color: #8d0306;
      font-size: 18px;
    }
  }

  .wrapper-main {
    display: flex;
  
    .left {
      border-right: 1px solid #e8eaec;
    }
  
    .left,
    .right {
      flex: 1;
    }
  }

  .wrapper-list {
    height: calc(100vh - 400px);
    min-height: 100px;
    padding: 10px;
    overflow: auto;
  
    .list-item {
      padding: 3px 17px;
      cursor: pointer;
  
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
  
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: fade(#000, 6%);
    border-radius: 3px;
    box-shadow: inset 0 0 5px fade(#000, 8%);
  }
  
  ::-webkit-scrollbar-thumb {
    background: fade(#000, 12%);
    border-radius: 3px;
    box-shadow: inset 0 0 10px fade(#000, 20%);
  }
}